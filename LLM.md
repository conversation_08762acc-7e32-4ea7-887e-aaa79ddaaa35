## 个人信息

- 姓名：汪俊尧		     性别：男		    年龄：27岁	    学历：本科
- 手机：17855112996      邮箱：<EMAIL>





## 求职意向

​	期望职位：大模型数据工程师							期望城市：上海

​	工作性质：全职							    		   工作状态：离职





## 教育经历

| 学校                 | 学历，专业     | 时间              |
| -------------------- | -------------- | ----------------- |
| 安徽文达信息工程学院 | 学士，软件工程 | 2018.09 - 2022.07 |





## 技能清单

- 熟悉计算机网络、数据结构和算法、Linux操作命令，能够独立在服务器上进行项目部署和运维

- 熟练使用 Java、Python编程，有较好的代码规范和调试能力

- 拥有AI模型工程化经验，熟悉目前主流的嵌入模型和大语言模型特性及应用

- 掌握常见Prompt优化技巧、RAG应用和自定义Agent等高级技巧

- 熟练掌握Django、Flask、FastAPI、Streamlit、Dash等技术

- 熟悉Numpy、Pandas、Matplotlib进行数据清洗和运算分析

- 熟悉SpringBoot/Cloud、MybatisPlus、Celery、RabbitMQ、Kafka、Redis等后端技术

- 熟悉Mysql、PostgreSql、Hbase、Elasticsearch、Minio做数据存储和查询检索

- 熟悉Milvus、Pinecone等向量数据库的使用

- 熟悉 TensrFlow、PyTorch进行AI模型开发和工程化

- 熟练掌握 GitLab使用规范，熟悉 CI/CD 生产及测试发布流程

  

## 工作经历

**上海台垣网络科技有限公司**（2021 年 9 月 ~ 2024 年 6 月 ）

- 工作内容：
  - 负责交易所数据资产管理，配合中台进行数据资源融合 
  - 支持运营部门提审的活动相关数据需求，整理经营分析数据模板 
  - 完善数仓建模设计和迭代，以及各需求方依赖的数据报表逻辑开发和投产分析
  - 负责公司外部数据的清洗转化融合类的工作，并配置相关任务调度，失败告警等 
  - 做好数据自测，利用自己对数据的感知度全盘分析数据的合理性，和同事做好交叉测试 
  - 需求开发完成，做好数仓脚本任务全流程调度，做好数据质检，出现的问题及时定位并解决



## 项目经历 

### 项目一：精准用户营销系统

- **所用技术** : DataWorks、Mysql、Hdfs、Hadoop、Python等
- **项目描述** : 本项目旨在开发一个基于LangChain框架和国内大模型技术的智能用户互动与营销系统。该系统通过调用先进的自然语言处理模型，分析用户行为和偏好，自动生成个性化的营销内容和推荐，提升用户在区块链交易所的参与度和转化率。项目包括用户数据收集与处理、自然语言理解与生成、个性化推荐算法的设计与实现、系统集成与部署等多个环节，实现从用户交互到个性化营销的全流程自动化。
- **工作内容** : 
  - 负责智能用户互动与营销系统的整体架构设计和技术选型，确保系统的高性能和扩展性
  - 使用LangChain和国内大模型进行用户行为和偏好的分析，提取用户数据中的潜在模式和趋势
  - 设计并实现数据清洗、预处理和特征工程流程，确保数据质量和模型效果
  - 开发并优化自然语言理解和生成模块，提高系统的响应准确性和用户体验
  - 实现个性化推荐算法，生成适合不同用户的营销内容和推荐
  - 将智能用户互动与营销系统部署到生产环境中，持续监控并优化系统性能
  - 与市场营销团队密切合作，确保系统生成的内容符合营销策略和用户需求
- **工作成果** : 
  - 营销内容的点击率提高了25%，转化率提升了20%
  - 数据处理和分析效率提升了35%，模型响应时间缩短了40%





### 项目二：交易策略推荐系统

- **所用技术** : DataWorks、Mysql、Hdfs、Hadoop、Python等

- **项目描述** : 本项目旨在开发一个基于大模型和机器学习算法的智能交易策略推荐系统。该系统通过分析用户的交易历史、市场趋势和实时数据，自动生成个性化的交易建议，帮助用户在区块链交易所中做出更明智的交易决策。项目包括数据收集与清洗、特征工程、模型训练与优化、系统集成与部署等多个环节，最终实现从数据到策略推荐的全流程自动化。
- **工作内容** : 
  - 负责系统的整体架构设计和技术选型，确保系统的高性能和扩展性
  - 使用大模型和机器学习算法进行数据挖掘和分析，提取交易数据中的潜在模式和趋势
  - 设计并实现数据清洗、预处理和特征工程流程，确保数据质量和模型效果
  - 开发并优化模型训练和评估管道，提高模型的准确性和稳定性
  - 将模型部署到生产环境中，持续监控并优化系统性能
  - 与产品经理和前端开发团队密切合作，确保交易策略推荐系统与用户界面的无缝集成。
- **工作成果** : 
  - 成功开发并上线了智能交易策略推荐系统，大幅提升了用户的交易体验和收益
  - 系统推荐策略的准确率提高了30%，用户反馈显著改善
  - 数据处理效率提升了40%，模型训练时间缩短了50%
  - 通过自动化部署和持续集成，提高了系统的可靠性和可维护性






### 项目三：基于大模型的智能风控系统

- **所用技术** : Python、Scala、TensorFlow、PyTorch、Kafka、Flink、PostgreSQL、HBase、Pandas、NumPy等

- **项目描述** :
- 本项目旨在开发一个基于大模型的智能风控系统，通过对区块链交易所的大量交易数据进行实时分析，自动识别潜在的风险交易行为和异常模式，提升平台的安全性和用户信任度。系统包括数据收集与处理、实时监控、风险评估模型的设计与实现、系统集成与部署等多个环节，实现从数据获取到风险预警的全流程自动化。
- **工作内容** : 
  - 负责智能风控系统的整体架构设计和技术选型，确保系统的高性能和扩展性
  - 开发和优化风险评估模型，利用大模型和机器学习算法进行实时数据分析和风险预测
  - 设计并实现数据清洗、预处理和特征工程流程，确保数据质量和模型效果
  - 集成Apache Kafka和Flink，实现实时数据流处理和监控，确保系统的低延迟和高可靠性
  - 将智能风控系统部署到生产环境中，持续监控并优化系统性能
  - 与安全团队密切合作，确保风险评估模型能够及时识别和响应潜在威胁
  - 编写系统文档和技术报告，确保系统的可维护性和可扩展性
  
- **工作成果** : 
  - 风险识别准确率提高了40%，实时监控的延迟减少了50%
  - 数据处理和分析效率提升了30%，系统响应时间缩短了35%

