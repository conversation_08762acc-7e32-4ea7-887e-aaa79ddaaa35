## 个人信息

​	姓名：汪俊尧		     性别：男		     年龄：26岁		     学历：本科

​	手机：17855112996      邮箱：<EMAIL>



## 求职意向

​	期望职位：大数据开发										期望城市：上海

​	工作性质：全职							    				工作状态：离职



## 技能清单

- **数据采集与集成方案：**
  - AWS生态：有过Glue与Kinesis结合使用的实际经验
  - 自建管道：使用过Kettle、DataX、Flume、Flink-CDC、调用三方的API获取、基于Python爬虫库等
- **数据分布式存储方案：**
  - AWS生态：使用过对象存储(S3)+RDS(PostgreSQL引擎)+数据仓库(Redshift)构建的数据开发服务体系

- **大数据组件：**
  - Flink：掌握窗口操作、状态管理、时间语义、CEP复杂事件处理、故障恢复，端到端的一致性等
  - Spark：掌握宽窄依赖、任务资源划分、容错机制、广播变量和累加器的使用以及故障排查和优化能力
  - Kafka：了解高水位、分区分配策略、高效读写原理、出现数据积压、数据倾斜、数据乱序等问题的解决方案
  - Hive：掌握HQL执行计划、窗口函数、存储压缩、分区和桶、动态分区、ACID事务表及性能优化的解决办法
- **BI报表工具：**使用过企业版完整的BI平台Metabase，也用过ECharts定制分析图表的方案
- **监控体系**：熟悉Prometheus监控K8s、Hadoop、Spark、Flink等集群健康状态，使用过PromQL语言将查询的结果通过接口返回给前端，也尝试过与Grafana集成提供图形化界面的方案。了解一些基础运维
- **数据生命周期治理与质量优化：**
  - 了解各内外部原始数据源的整合入湖-清洗-融合-加工-计算-测试-上线-异常回滚的关键步骤及模型规范
  - 了解数据质量评估流程，数据探查-校验-对账-稽核-入表及自动化方案的实施
  - 了解安全可信的数据共享方案，编写过Xacml访问控制策略用例，用于属性的访问控制ABAC数据连接器模型
- **编程能力：**
  - 熟悉Java基础，参与过前后端的项目，如钱包对账，数字运营一体化平台，自定义分区器，函数等
  - 熟悉Scala基础，在历史数据回测和用户行为分析等场景下，基于Dataset API 进行处理
  - 熟悉Python基础，部分场景下，做过数据清洗和处理，自动化导入任务的脚本


- **Devops**:  熟练掌握 GitLab使用规范，熟悉 CI/CD 生产及测试发布流程

  

## 工作经历

**上海德拓信息技术股份有限公司**（2023 年 7 月 ~ 2024 年 4 月 ）

- **工作内容：**
  - 设计普惠金融税务指标和企业特征模型，摸排源头数据，并将数据清洗落库 
  - 通过数据集成将数据同步到数仓进行数据融合加工处理，转化为产品的应用层指标
  - 通过同比，环比，异常值等对数据质量进行评分，编写数据报告 
  - 通过集成再将数据推到本地数据库，供业务服务调用
  - 配合完成数据质量评估流程，如数据探查、数据校验、数据对账等工作



**上海台垣网络科技有限公司**（2021 年 9 月 ~ 2023 年 6 月 ）

- **工作内容：**
  - 负责交易所数据资产管理，配合中台进行数据资源融合 
  - 支持运营部门提审的活动相关数据需求，整理经营分析数据模板 
  - 完善数仓建模设计和迭代，以及各需求方依赖的数据报表逻辑开发和投产分析
  - 负责公司外部数据的清洗转化融合类的工作，并配置相关任务调度，失败告警等 
  - 做好数据自测，利用自己对数据的感知度全盘分析数据的合理性，和同事做好交叉测试 
  - 需求开发完成，做好数仓脚本任务全流程调度，做好数据质检，出现的问题及时定位并解决



## 项目经历 

### 项目一：工商企业智能画像系统（2023年07～2024年4月）

- **所用技术：**Hadoop、Spark、Hive、Flink、Kettle、Flume、Kafka、Docker、SpringBoot、Vue2等
- **项目描述：**上海联合征信有限公司致力于为金融机构、企业和个人提供全面、精准的信用评估服务。面对日益增长的市场需求和复杂多变的经济环境，公司决定开发一套企业智能画像系统，旨在通过深度分析内外部数据，构建全面的企业信用和经营状况画像，提升信用评估的准确性和时效性。
- **项目目标：**整合企业工商注册信息、财务数据、行业动态、市场表现、法律诉讼记录及社交媒体评价等多维度数据，构建企业全景画像，利用机器学习算法，实时监测企业经营风险，提前预警潜在的信用违约事件
- **项目责任：**
  - 参与系统整体架构设计调研会，确保系统的高可用性、可扩展性和安全性
  - 构建多源数据采集管道，归集各委办数据，并进行清洗和预处理
  - 构建实时数据处理流，对企业的最新动态进行分析和处理
  - 针对数据问题记录追踪，定期审计评估并提供解决方案
  - 负责数据处理接口和可视化仪表盘交互模块的开发部署
  - 对于数据的异常情况及时修正并上报，确保数据的安全合规推送
  - 负责任务的监控告警和部分数据服务的收尾工作







### 项目二：xt实时数仓建设（2022年8月～2023年5月）

- **所用技术：**Java、Flink、Kafka，Redis、Hbase、Duboo、TiDB、ClickHouse、RocksDB、Hdfs、Elasticsearch等
- **项目描述：**该项目是基于离线数仓修改调度频率依旧难以满足数据实时性的要求，一方面，在面对产品需求和集团内部决策中包含实时 OLAP 分析和看板以及实时业务监控和风控能力时很难支撑，另一方面，实时数据建设没有规范，数据可用性较差，无法形成数仓体系，资源大量浪费。因此，以数仓建设理论和实时技术，解决由于当前离线数仓数据时效性低解决不了的问题，便是实时数仓建设的目的。
- **项目责任：**  
  - 参与风控产品需求会议,分析源库表及埋点日志存储路径，并统一指标计算公式和口径
  - 参与实时数仓模型设计，调研并在测试环境部署调试，确认实时计算引擎和存储架构
  - 进行实时日志数据的ETL清洗，关联维度表在明细中间层做维度补全，调用Duboo接口UDF对丢失数据兜底
  - 针对任务异常失败重启时kafaka重复消费问题，做幂等处理，已消费数据不再下发
  - 处理数据延迟和乱序问题，根据数据量级、恢复速度、资源消耗等因素选择合适的状态后端
  - 通过FlinkSQL输出实时特征指标，写入Clickhouse，提供高效的多维实时查询接口去读取数据
  - 监控实时数仓的运行状态，定期进行性能调优，根据实时数据流量动态调整资源配置，避免资源浪费
  - 进行抽样验证与维表全量验证，主要与离线数仓进行数据对账，确保数据完整性和一致性
  - 完善实时计算分析架构，具备为运营，财务，风控，业务提供全覆盖实时数据的能力





### 项目三：xt数据资产稽核治理（2022年3月～2022年10月）

- **所用技术：**SpringBoot、Vue、MyBatis Plus、Mysql、Prometheus、Atlas、Spark、Confluence等
- **项目描述：** 随着业务营销活动的快速迭代，数据仓库的库表越来越繁多，初期搭建时确定的数据标准、模型设计规范略显粗糙，缺少一套完整数据的生命周期管理体系，导致数仓中沉积了大量没有下游引用的无效数据表。由于开发不规范导致的分层混乱、层级反向依赖、ODS层穿透等现象也越来越多。需求紧张也产生了大量烟囱式数据表和重复开发指标等问题。为释放集群资源，减少数据质量问题，急需对数仓进行规范化治理，更好地发挥数据价值。
- **项目责任：**  
  - 参与梳理数仓目前存在的问题，将问题归类并记录总结，负责调研筹备数据治理的准备工作
  - 维护元数据管理系统，记录数据的业务定义、来源、使用情况和数据血缘关系，定期检查与源的一致性
  - 解决历史遗留的小文件问题，整理小文件过多的表，通过参数配置合并小文件，同时对新增小文件的表做扎口
  - 针对数仓大作业，高频调度的任务，长期异常、无效、空跑以及孤儿任务等进行治理，规划数仓任务归属
  - 结合业务痛点新增数十个数据质量监控规则，梳理公司核心数据表，配置合理的DQC规则，多维监控数据质量
  - 解决ODS穿透表的治理，减少数据冗余，应用侧共性指标下沉，针对公共层模型复用率情况等进行治理
  - 生产库表的梳理，生产表生命周期的上线，超期未访问的数据治理，对不合理存在的表进行下线清理
  - 使用Prometheus 监控数据处理流程和整个服役节点的健康状态，针对异常情况和瓶颈及时修复
  - 将作业流任务告警与工作台对接，搭建告警服务推送机器人，并能根据标签去向量知识库中查询最优应答策略
  - 严格按照数据测试流程规范化、标准化，提高数据的质量和可靠性





### 项目四：xt离线数仓2.0～3.0研发（2021年10月～2022年8月）

- **所用技术：**AWS Glue、Hive、Spark、DolphinScheduler、Metabase、PostgreSQL、Elasticsearch、AWS RedShift等

- **项目描述：**随着公司的高速发展，数据需求及应用场景越来越多，数据处理和分析已经成为业务运营日常工作，由于前期缺乏对数据开发的统一管理标准，导致数据口径不统一．逻辑不清晰、跨部门取数分析难，难以快速满足复杂多变的分析需求。因此，急需搭建数据中台，构建共享数据服务体系，提升公共层能力和数据应用场景，实现数据可共享、可复用，可追溯，为前台提供数据资产、数据分析与数据监测、数据备份等服务，最终实现数据资产的价值最大化。
- **项目责任：**主要参与了数据采集清洗，数据产品研发，数据监控，报表推送等工作。
  - 梳理业务数据需求优先级标准，高优先需求小时级响应，中优先级需求当日响应，支撑各业务顺利进行，不影响整体项目进度，做好数据内部自测和交叉测试，保证资产类需求零bug
  - 完善数据指标体系建设，整理数据间的关系、数据的粒度、数据的层次结构等，结合当前的业务体系规范数仓层级 ，持续进行离线数仓建设，提升需求开发效率，快速定位产品需求落地需要的数据
  - 负责数据迁移同步，做好数据抽取脱敏清洗转换加载的过程，确保ETC工作不出错
  - 负责定时任务调度管理，产品加工调度，批量查询调度，数据更新推送
  - 参与上线自动API配置，减少报表类功能冗余，降低开发工时
  - 构建数据交换空间，定义连接器规则，实现细粒度数据行列级别的权限控制，用后即焚
  - 将部分交易与活动数据通过API调用，共享存储，采集同步等方式提供给量化模型开发组
  - 参与上线数据质量监控，重要业务数据监控全覆盖

