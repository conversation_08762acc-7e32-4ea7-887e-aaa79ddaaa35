## 个人信息

​	姓名：汪俊尧		     性别：男		     年龄：26岁		     学历：本科

​	手机：17855112996      邮箱：<EMAIL>



## 求职意向

​	期望职位：大数据开发										期望城市：上海

​	工作性质：全职							    				工作状态：离职



## 技能清单

- **计算机基础** ：熟悉计算机网络、数据结构和算法、熟悉 Linux操作命令

- **开发语言**：熟练使用 Java、Python、Scala 编程

- **开发工具** ：熟练使用 Maven/Gradle、Git、IDEA 、PyCharm 、Docker 等开发工具

- **数据库**：熟悉 MySQL、DM8、PostgreSQL、Redis、Hbase、Clickhose等数据库的使用

- **大数据组件**：

  - 熟悉Flink流处理框架，能够使用DataStreamApi和FlinkSQL处理实时业务需求，掌握常用窗口及函数、状态编程 、CEP编程、FlinkCDC、以及如何保证端到端的一致性，出现反压，数据倾斜等问题的调优手段
  - 熟悉 Spark RDD机制及其底层执行原理，宽窄依赖，任务资源划分，核心Shuffle及其调优，算子调优等
  - 熟悉Kafka架构，高水位，分区分配策略、高效读写原理，使用Kafka组件作为数据缓冲层，各业务模块解耦，遇到 数据积压、数据倾斜、数据乱序等问题的调优手段
  - 熟悉Hive执行计划，HQL操作，常用函数，存储压缩，针对数据倾斜等问题的优化手段
  - 熟悉Hdfs读写流程，Shuffle 工作流程，Yarn 调度模式等
  - 熟悉DolphinScheduler做数仓全链路工作流调度

- **大模型应用及多模态**：

  - 熟悉Numpy、Pandas、Matplotlib进行数据清洗和处理
  - 熟练使用jieba分词器进行自然语言预处理、关键词提取和词性标注等
  - 掌握常规Prompt优化技巧、提示词工程、RAG应用和自定义Agent等高级技巧
  - 熟悉大语言模型应用开发框架如 LangChain、LlamaIndex，对AIGC应用有强烈兴趣
  - 熟悉 HuggingFace Transformers 开源库中常规预训练模型的使用，能基于开源模型进行迁移学习和微调
  - 熟悉Milvus、Faiss等开源向量数据库的使用

- **Devops** : 熟练掌握 GitLab使用规范，熟悉 CI/CD 生产及测试发布流程

  

## 工作经历

**上海金亥通信设备有限公司**（2023 年 7 月 ~ 2024 年 4 月 ）

- **工作内容**：
  - 设计普惠金融税务指标和企业特征模型，摸排源头数据，并将数据清洗落库 
  - 通过数据集成将数据同步到数仓进行数据加工处理，转化为产品的应用层指标
  - 通过同比，环比，异常值等对数据质量进行评分，编写数据报告 
  - 通过集成再将数据推到本地数据库，供业务服务调用
  - 参与公司征信评分模型和征信系统的研发，如反欺诈模型、信用评分、催收评分、情感分析等模型设计开发
  - 建立和测试大数据风控模型，并协助完成数据预处理、模型优化和机器学习算法更新



**上海台垣网络科技有限公司**（2021 年 9 月 ~ 2023 年 6 月 ）

- 工作内容：
  - 负责交易所数据资产管理，配合中台进行数据资源融合 
  - 支持运营部门提审的活动相关数据需求，整理经营分析数据模板 
  - 完善数仓建模设计和迭代，以及各需求方依赖的数据报表逻辑开发和投产分析
  - 负责公司外部数据的清洗转化融合类的工作，并配置相关任务调度，失败告警等 
  - 做好数据自测，利用自己对数据的感知度全盘分析数据的合理性，和同事做好交叉测试 
  - 需求开发完成，做好数仓脚本任务全流程调度，做好数据质检，出现的问题及时定位并解决



## 项目经历 

### 项目一：智能营销助手

- **所用技术** : Python、BERT、TensorFlow、Langchain、FAISS、MySQL、Redis等
- **项目描述** : 智能营销助手是为电信业务办理咨询等服务开发的营销数字人，旨在提升用户体验并优化客户服务流程。系统采用较新的自然语言处理和机器学习技术，通过知识库和向量库实现对用户问题的自动回答，并在知识库未命中的情况下调用大语言模型生成高质量的回答。此外，系统支持多模态交互，用户可以通过文本或语音进行提问，系统能够实时识别并处理。同时，系统通过分析用户历史数据和行为，提供个性化的产品和服务推荐。
- **工作内容** : 
  - 负责现场问题处理记录，更新知识库和大模型敏感词优化
  - 使用BERT模型对知识库进行向量化，构建高效的FAISS向量检索库
  - 集成Langchain，通过API调用预训练好的大语言模型生成答复
  - 实现文本和语音输入的识别处理与对齐，确保系统支持多模态交互
  - 通过数据分析和用户行为建模，开发个性化推荐算法
  - 根据用户反馈不断优化知识库和回答策略，提升用户体验





### 项目二：工商企业智能画像系统

- **所用技术** : Java、Flink、Mysql、HBase、Kafka、SpringBoot、Vue等

- **项目描述** : 工商企业智能画像系统旨在为公司提供精确的企业信用评估和风险分析。系统利用多源数据，包括工商登记、财务报表、市商等数据，构建企业的全面画像。通过大数据处理与分析技术，结合机器学习和自然语言处理算法，实现对企业的信用评级、风险预警、经营状况预测等功能。帮助企业做出更加准确的商业决策，以及挖掘数据价值。
- **工作内容** : 
  - 参与系统的整体架构设计，确保系统的高可用性、可扩展性和安全性
  - 构建多数据源数据采集管道，归集各委办数据，并进行清洗和预处理
  - 使用Flink构建实时数据处理流，对企业的最新动态进行分析和处理，生成实时画像
  - 负责数据处理接口和可视化仪表盘交互模块的开发部署
  - 对于数据的异常情况及时修正并上报，确保数据的安全合规推送
  - 负责任务的监控告警和部分数据服务的收尾工作





### 项目三：xt实时数仓建设

- **所用技术** : Java、Flink、Kafka，Redis、Hbase、Duboo、TiDB、ClickHouse、RocksDB、Hdfs、Elasticsearch等

- **项目描述** : 该项目是基于离线数仓修改调度频率依旧难以满足数据实时性的要求，一方面，在面对产品需求和集团内部决策中包含实时 OLAP 分析和看板以及实时业务监控和风控能力时很难支撑，另一方面，实时数据建设没有规范，数据可用性较差，无法形成数仓体系，资源大量浪费。因此，以数仓建设理论和实时技术，解决由于当前离线数仓数据时效性低解决不了的问题，便是实时数仓建设的目的。

- **工作内容** : 

  - 参与风控产品需求会议,分析源库表及埋点日志存储路径，并统一指标计算公式和口径

  - 参与实时数仓模型设计，调研并在测试环境部署调试，确认实时计算引擎和存储架构

  - 进行实时日志数据的ETL清洗，关联维度表在明细中间层做维度补全，调用Duboo接口UDF对丢失数据兜底
  - 针对任务异常失败重启时kafaka重复消费问题，做幂等处理，已消费数据不再下发
  - 通过FlinkSQL输出实时特征指标，写入Clickhouse，提供高效的多维实时查询接口去读取数据
  - 进行抽样验证与维表全量验证，主要与离线数仓数据进行对比，也就是数据对账，确保数据完整性和一致性
  - 完善实时计算分析架构，具备为运营，财务，风控，业务提供全覆盖实时数据的能力





### 项目四：_xt_数据治理项目

- **所用技术**：SpringBoot、Vue、MyBatis Plus、Mysql、Prometheus、Atlas、Spark、Confluence等
- **项目描述** : 随着业务营销活动的快速迭代，数据仓库的库表越来越繁多，初期搭建时确定的数据标准、模型设计规范略显粗糙，缺少一套完整数据的生命周期管理体系，导致数仓中沉积了大量没有下游引用的无效数据表。由于开发不规范导致的分层混乱、层级反向依赖、ODS层穿透等现象也越来越多。需求紧张也产生了大量烟囱式数据表和重复开发指标等问题。为释放集群资源，减少数据质量问题，急需对数仓进行规范化治理，更好地发挥数据价值。
- **工作内容** : 
  - 参与梳理数仓目前存在的问题，将问题归类并记录总结，负责调研筹备数据治理的准备工作
  - 解决历史遗留的小文件问题，整理小文件过多的表，通过参数配置合并小文件，同时对新增小文件的表做扎口
  - 针对数仓大作业，高频调度的任务，长期异常、无效、空跑以及孤儿任务等进行治理，规划数仓任务归属
  - 结合业务痛点新增数十个数据质量监控规则，梳理公司核心数据表，配置合理的DQC规则，多维监控数据质量
  - 解决ODS穿透表的治理，应用侧共性指标下沉，针对公共层模型复用率情况等进行治理
  - 生产库表的梳理，生产表生命周期的上线，超期未访问的数据治理，对不合理存在的表进行下线清理
  - 严格按照数据测试流程规范化、标准化，提高数据的质量和可靠性





### 项目五：数字资产交易所离线数仓研发

- **所用技术** : AWS Glue、Hive、Spark、DolphinScheduler、Metabase、PostgreSQL、Elasticsearch、GitLab、S3等

- **项目描述** : 随着公司的高速发展，数据需求及应用场景越来越多，数据处理和分析已经成为业务运营日常工作，由于前期缺乏对数据开发的统一管理标准，导致数据口径不统一．逻辑不清晰、跨部门取数分析难，难以快速满足复杂多变的分析需求。因此，急需搭建数据中台，构建共享数据服务体系，提升公共层能力和数据应用场景，实现数据可共享、可复用，可追溯，为前台提供数据资产、数据分析与数据监测、数据备份等服务，最终实现数据资产的价值最大化。
- **工作内容** : 主要参与了ETL清洗抽数，数据开发脚本编写，报表推送等工作。
  - 梳理业务数据需求优先级标准，高优先需求小时级响应，中优先级需求当日响应，支撑各业务顺利进行，不影响整体项目进度，做好数据内部自测和交叉测试，保证资产类需求零bug
  - 完善数据指标体系建设，整理数据间的关系、数据的粒度、数据的层次结构等，结合当前的业务体系规范数仓层级 ，持续进行离线数仓建设，提升需求开发效率，快速定位产品需求落地需要的数据
  - 负责数据迁移同步，做好数据抽取脱敏清洗转换加载的过程，确保ETC工作不出错
  - 负责定时任务调度管理，产品加工调度，批量查询调度，数据更新推送
  - 参与上线自动API配置，减少报表类功能冗余，降低开发工时
  - 构建数据交换空间，定义连接器规则，实现细粒度数据行列级别的权限控制，用后即焚
  - 将部分交易与活动数据通过API调用，共享存储，采集同步等方式提供给量化模型开发组
  - 参与上线数据质量监控，重要业务数据监控全覆盖

