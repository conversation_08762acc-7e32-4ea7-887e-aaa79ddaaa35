## 个人信息

- 姓名：汪俊尧		     性别：男		    年龄：27岁	    学历：本科
- 手机：17855112996      邮箱：<EMAIL>



## 求职意向

​	期望职位：大模型数据工程师							期望城市：上海

​	工作性质：全职							    		   工作状态：离职



## 教育经历

| 学校                 | 学历，专业     | 时间              |
| -------------------- | -------------- | ----------------- |
| 安徽文达信息工程学院 | 学士，软件工程 | 2018.09 - 2022.07 |



## 技能清单

- 熟悉计算机网络、数据结构和算法、Linux操作命令，能够独立在服务器上进行项目部署和运维

- 熟练使用 Java、Python编程，有较好的代码规范和调试能力

- 拥有AI模型工程化经验，熟悉目前主流的嵌入模型和大语言模型特性及应用

- 掌握常见Prompt优化技巧、RAG应用和自定义Agent等高级技巧

- 熟练掌握Django、Flask、FastAPI、Streamlit、Dash等技术

- 熟悉Numpy、Pandas、Matplotlib进行数据清洗和运算分析

- 熟悉SpringBoot/Cloud、MybatisPlus、Celery、RabbitMQ、Kafka、Redis等后端技术

- 熟悉Mysql、PostgreSql、Hbase、Elasticsearch、Minio做数据存储和查询检索

- 熟悉Milvus、Pinecone等向量数据库的使用

- 熟悉 TensrFlow、PyTorch进行AI模型开发和工程化

- 熟练掌握 GitLab使用规范，熟悉 CI/CD 生产及测试发布流程

  

## 工作经历

**上海台垣网络科技有限公司**（2021 年 9 月 ~ 2023 年 6 月 ）

- 工作内容：
  - 负责交易所数据资产管理，配合中台进行数据资源融合 
  - 支持运营部门提审的活动相关数据需求，整理经营分析数据模板 
  - 完善数仓建模设计和迭代，以及各需求方依赖的数据报表逻辑开发和投产分析
  - 负责公司外部数据的清洗转化融合类的工作，并配置相关任务调度，失败告警等 
  - 做好数据自测，利用自己对数据的感知度全盘分析数据的合理性，和同事做好交叉测试 
  - 需求开发完成，做好数仓脚本任务全流程调度，做好数据质检，出现的问题及时定位并解决



## 项目经历 

### 项目一：工商企业画像

- **所用技术** : DataWorks、Mysql、Hdfs、Hadoop、Python等

- **项目描述** : 我们的目标是开发一套数据脚本（主要是包含征信和税务），对数据集团以及大数据中心申请下发的各委办数据做清洗融合，以便处理企业各维度的数据，实现快速、精确的数据处理和分析，并提供可靠的结果给客户。
- **工作内容** : 
  - 根据大数据中心公共数据门户以及金融机构收集征信相关数据，并进行数据清洗和验证，确保数据质量和一致性
  - 数据探查并整理各委办数据字典, 如数据源表、清洗后落地表、各字段空值率，使用率等标识
  - 根据模型指标在 DWD 层进行字段格式统一,正则提取相关数据等数据清洗工作
  - 设计普惠3.0税务、科创企业特征、企业划型、及各银行区域评分类指标
  - 调整 hive 参数进行数据倾斜,大小表 join 的优化提升运行质量
  - 梳理应用层指标，完成公共计算逻辑下沉，并将指标落到DM层
  - 配置全链路数据质量监控和修复，防止问题数据流入下游任务
  - 数据开发完毕后，通过数据集成将数据推送出去



### 项目二：_xt_数据治理项目

- **项目描述** : 随着业务运营活动的快速迭代，数据仓库的库表越来越繁多，初期搭建时确定的数据标准、模型设计规范略显粗糙，且缺少一套完整数据的生命周期管理体系，导致数仓中沉积了大量没有下游引用的无效数据表。由于开发不规范导致的分层混乱、层级反向依赖、ODS层穿透等现象也越来越多。需求紧张也产生了大量烟囱式数据表和重复开发指标等问题。为释放集群资源，减少数据质量问题，急需对数仓进行规范化治理，更好地发挥数据价值。
- **工作内容** : 
  - 参与梳理数仓目前存在的问题，将问题归类并记录总结，负责调研筹备数据治理的准备工作。
  - 解决历史遗留的小文件问题，整理小文件过多的表，通过参数配置合并小文件，同时对新增小文件的表进行扎口处理
  - 针对数仓大作业，高频调度的任务，长期异常、无效、空跑以及孤儿任务等进行治理，规划数仓任务归属与管理
  - 结合业务痛点新增数十个数据质量监控规则，梳理公司核心的数据表，配置合理的DQC规则，多维度监控数据质量
  - 解决ODS穿透表的治理，应用侧共性指标下沉，针对公共层模型复用率情况等进行治理
  - 生产库表的梳理，生产表生命周期的上线，超期未访问的数据治理，对不合理存在的表进行下线清理
  - 严格按照数据测试流程规范化、标准化，提高数据的质量和可靠性



### 项目三：数字资产交易所离线数仓研发

- **所用技术** : AWS Glue、Hive、Spark、DolphinScheduler、Metabase、PostgreSQL、Elasticsearch、GitLab、S3等

- **项目描述** : 随着公司的高速发展，数据需求及应用场景越来越多，数据处理和分析已经成为业务运营日常工作，由于前期缺乏对数据开发的统一管理标准，导致数据口径不统一．逻辑不清晰、跨部门取数分析难，难以快速满足复杂多变的分析需求。因此，急需搭建数据中台，构建共享数据服务体系，提升公共层能力和数据应用场景，实现数据可共享、可复用，可追溯，为前台提供数据资产、数据分析与数据监测、数据备份等服务，最终实现数据资产的价值最大化。
- **工作内容** : 主要参与了ETL清洗抽数，数据开发脚本编写，报表推送等工作。
  - 梳理业务数据需求优先级标准，高优先需求小时级响应，中优先级需求当日响应，支撑各业务顺利进行，不影响整体项目进度，做好数据内部自测和交叉测试，保证资产类需求零bug
  - 完善数据指标体系建设，整理数据间的关系、数据的粒度、数据的层次结构等，结合当前的业务体系规范数仓层级 ，持续进行离线数仓建设，提升需求开发效率，快速定位产品需求落地需要的数据
  - 负责数据迁移同步，做好数据抽取脱敏清洗转换加载的过程，确保ETC工作不出错
  - 负责定时任务调度管理，产品加工调度，批量查询调度，数据更新推送
  - 参与上线自动API配置，减少报表类功能冗余，降低开发工时
  - 参与上线数据质量监控，重要业务数据监控全覆盖

