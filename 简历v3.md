## 个人信息

- 姓名：汪俊尧		     性别：男		    年龄：27岁	    学历：全日制本科（2018.9～2022.7）
- 手机：17855112996      邮箱：<EMAIL>         学校：安徽文达信息工程学院



## 技能清单

- **计算机基础** ：熟悉计算机网络、数据结构和算法、熟悉 Linux操作命令

- **开发语言**：熟练使用 Java、Python、Scala、Go

- **开发工具** ：熟练使用 Maven/Gradle、Git、IDEA 、PyCharm 、Docker 等开发工具

- **数据库**：熟悉 MySQL，MongoDB，DM8，PostgreSQL，Redis，Hbase，Doris，Clickhose数据库的使用

- **大数据组件**：
  
  - 熟悉Flink流处理框架，能够使用DataStreamApi和FlinkSQL处理实时业务需求，掌握常用窗口及函数、状态编程 、CEP编程、FlinkCDC、以及如何保证端到端的一致性，出现反压，数据倾斜等问题的调优手段
  - 熟悉 Spark RDD机制及其底层执行原理，宽窄依赖，任务资源划分，核心Shuffle及其调优，算子调优等
  - 熟悉Kafka架构，高水位，分区分配策略、高效读写原理，使用Kafka组件作为数据缓冲层，各业务模块解耦，遇到数据积压、数据倾斜、数据乱序等问题的调优手段
  - 熟悉Hive执行计划，HQL操作，常用函数，存储压缩，针对数据倾斜等问题的优化手段
  - 熟悉Hdfs读写流程，Shuffle 工作流程，Yarn 调度模式等
  - 熟悉DolphinScheduler调度框架
  
- **Devops** : 熟练掌握 GitLab使用规范，熟悉 CI/CD 生产及测试发布流程

  

## 工作经历

**上海台垣网络科技有限公司**（2021 年 9 月 ~ 2024 年 5 月 ）

- 工作内容：
  - 负责交易所数据资产管理，配合中台进行数据资源融合 
  - 支持运营部门提审的活动相关数据需求，整理经营分析数据模板 
  - 完善离线数仓建模设计和迭代，以及各需求方依赖的数据报表逻辑开发和投产分析
  - 负责公司外部数据的清洗转化融合类的工作，并配置相关任务调度，失败告警等 
  - 做好数据自测，利用自己对数据的感知度全盘分析数据的合理性，和同事做好交叉测试 
  - 需求开发完成，做好数仓脚本任务全流程调度，做好数据质检，出现的问题及时定位并解决
  - 负责数据开发部分集群的运维和新任务上线发布，业务联调的相关工作
  - 统筹实时数仓的初期研发及数据治理的调研准备和部分实施工作

1. **完全解决播放问题**: 通过先统一编码再合并的策略
2. **参数完全一致**: 所有44个视频重新编码到相同参数
3. **无缝播放**: 视频切换点无任何异常
4. **高质量输出**: CRF 23保证良好画质

**上海智能科学研究院**（2024 年 5 月 ~ 2025 年 7 月 ）

- 工作内容：

  - 完成智能数智平台，

  - 支持运营部门提审的活动相关数据需求，整理经营分析数据模板 

  - 完善离线数仓建模设计和迭代，以及各需求方依赖的数据报表逻辑开发和投产分析

  - 负责公司外部数据的清洗转化融合类的工作，并配置相关任务调度，失败告警等 

  - 做好数据自测，利用自己对数据的感知度全盘分析数据的合理性，和同事做好交叉测试 

  - 需求开发完成，做好数仓脚本任务全流程调度，做好数据质检，出现的问题及时定位并解决

  - 负责数据开发部分集群的运维和新任务上线发布，业务联调的相关工作

  - 统筹实时数仓的初期研发及数据治理的调研准备和部分实施工作



## 项目经历 

### 项目一：xt实时数仓建设

- **所用技术** : Java、Flink、Kafka，Redis，Hbase，Duboo, TiDB，ClickHouse、RocksDB、Hdfs、Elasticsearch等

- **项目描述** : 该项目是基于离线数仓修改调度频率依旧难以满足数据实时性的要求，一方面，在面对产品需求和集团内部决策中包含实时 OLAP 分析和看板以及实时业务监控和风控能力时很难支撑，另一方面，实时数据建设没有规范，数据可用性较差，无法形成数仓体系，资源大量浪费。因此，以数仓建设理论和实时技术，解决由于当前离线数仓数据时效性低解决不了的问题，便是实时数仓建设的目的。

- **工作内容** : 
  
  - 参与风控产品需求会议,分析源库表及埋点日志存储路径，并统一指标计算公式和口径
  
  - 参与实时数仓模型设计，调研并在测试环境部署调试，确认实时计算引擎和存储架构
  
  - 进行实时日志数据的ETL清洗，关联维度表在明细中间层做维度补全，调用Duboo接口UDF对丢失数据兜底
  - 针对任务异常失败重启时kafaka重复消费问题，做幂等处理，已消费数据不再下发
  - 通过FlinkSQL输出实时特征指标，写入Clickhouse，提供高效的多维实时查询接口去读取数据
  - 进行抽样验证与维表全量验证，主要与离线数仓数据进行对比，也就是数据对账，确保数据完整性和一致性
  - 完善实时计算分析架构，具备为运营，财务，风控，业务提供全覆盖实时数据的能力





### 项目二：_xt_数据治理项目

- **所用技术**：SpringBoot，Vue，MyBatis Plus，Mysql，Prometheus，Atlas，Spark，Confluence，Docker，GitLab等

- **项目描述** : 随着业务运营活动的快速迭代，数据仓库的库表越来越繁多，初期搭建时确定的数据标准、模型设计规范略显粗糙，且缺少一套完整数据的生命周期管理体系，导致数仓中沉积了大量没有下游引用的无效数据表。由于开发不规范导致的分层混乱、层级反向依赖、ODS层穿透等现象也越来越多。需求紧张也产生了大量烟囱式数据表和重复开发指标等问题。为释放集群资源，减少数据质量问题，急需对数仓进行规范化治理，更好地发挥数据价值。
- **工作内容** : 
  - 参与梳理数仓目前存在的问题，将问题归类并记录总结，负责调研筹备数据治理的准备工作
  - 解决历史遗留的小文件问题，整理小文件过多的表，通过参数配置合并小文件，同时对新增小文件的表做扎口
  - 针对数仓大作业，高频调度的任务，长期异常、无效、空跑以及孤儿任务等进行治理，规划数仓任务归属
  - 结合业务痛点新增数十个数据质量监控规则，梳理公司核心数据表，配置合理的DQC规则，多维监控数据质量
  - 解决ODS穿透表的治理，应用侧共性指标下沉，针对公共层模型复用率情况等进行治理
  - 生产库表的梳理，生产表生命周期的上线，超期未访问的数据治理，对不合理存在的表进行下线清理
  - 严格按照数据测试流程规范化、标准化，提高数据的质量和可靠性





### 项目三：数字资产交易所离线数仓研发

- **所用技术** : AWS Glue、Hive、Spark、DolphinScheduler、Metabase、PostgreSQL、Elasticsearch、GitLab、S3等

- **项目描述** : 随着公司的高速发展，数据需求及应用场景越来越多，数据处理和分析已经成为业务运营日常工作，由于前期缺乏对数据开发的统一管理标准，导致数据口径不统一．逻辑不清晰、跨部门取数分析难，难以快速满足复杂多变的分析需求。因此，急需搭建数据中台，构建共享数据服务体系，提升公共层能力和数据应用场景，实现数据可共享、可复用，可追溯，为前台提供数据资产、数据分析与数据监测、数据备份等服务，最终实现数据资产的价值最大化。
- **工作内容** : 主要参与了ETL清洗抽数，数据开发脚本编写，报表推送等工作。
  - 梳理业务数据需求优先级标准，高优先需求小时级响应，中优先级需求当日响应，支撑各业务顺利进行，不影响整体项目进度，做好数据内部自测和交叉测试，保证资产类需求零bug
  - 完善数据指标体系建设，整理数据间的关系、数据的粒度、数据的层次结构等，结合当前的业务体系规范数仓层级 ，持续进行离线数仓建设，提升需求开发效率，快速定位产品需求落地需要的数据
  - 负责数据迁移同步，做好数据抽取脱敏清洗转换加载的过程，确保ETC工作不出错
  - 负责定时任务调度管理，产品加工调度，批量查询调度，数据更新推送
  - 参与上线自动API配置，减少报表类功能冗余，降低开发工时
  - 构建数据交换空间，定义连接器规则，实现细粒度数据行列级别的权限控制，用后即焚
  - 将部分交易与活动数据通过API调用，共享存储，采集同步等方式提供给量化模型开发组
  - 参与上线数据质量监控，重要业务数据监控全覆盖



### 项目四：数仓钱包数据对账

- **所用技术** : AWS Glue、Hive、Spark、DolphinScheduler、Metabase、PostgreSQL、Elasticsearch、GitLab、S3等
- **项目描述** : 财务部门需要对租户的资金划转、充值、未归集计算出理论余额，再同实际余额比对差异进行对账，包括冷钱包对账，平台总盈亏，平台手续费报表，财务现货实时对冲，合约资产表，风险大账户，资金费率日报，合约盈亏明 细表，以及特殊账户资产表等等，需要确认产出指标的计算口径，然后在数仓开发对应的数据报表写到系统里面，以便财务人员可以直接在系统里进行检索，提升对账效率。钱包三方对账是根据钱包开发部门推送的交易所的地址划转余额数据，最终通过接口根据地址、币链关系、块高，去三方公链上查询对应地址的余额和划转数据，将查到的结果落到Mongo里面，然后T+1同步到数仓，根据定好的计算公式，在数仓里完成对账操作，找出差异数据并分析，将分析结果推送给相关负责人，参与完成BTC\ETH\ESC\TRX四大链的开发。
- **工作内容** : 
  - 完善财务自动对账，匹配财务对账逻辑，提升对账效率
  - 参与上线提币自动审核，减少人工审核成本
  - 参与上线租户财报、业务报表，辅助租户运营管理
  - 完善BD绩效、带跟单系统，并持续迭代
  - 完成钱包对账开发，钱包地址余额和划转基于公链校准
